/**
 * 用户相关API接口
 */

export const userApi = {
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise} 返回登录结果
   */
  login: (credentials) => {
    return window.$http.post('/api/auth/login', credentials)
  },

  /**
   * 用户退出登录
   * @returns {Promise} 返回操作结果
   */
  logout: () => {
    return window.$http.post('/api/auth/logout')
  },

  /**
   * 获取用户信息
   * @returns {Promise} 返回用户信息
   */
  getUserInfo: () => {
    return window.$http.fetch('/api/user/profile')
  },

  /**
   * 更新用户信息
   * @param {Object} data - 用户信息
   * @returns {Promise} 返回操作结果
   */
  updateUserInfo: (data) => {
    return window.$http.put('/api/user/profile', data)
  },

  /**
   * 修改密码
   * @param {Object} data - 密码数据
   * @param {string} data.oldPassword - 旧密码
   * @param {string} data.newPassword - 新密码
   * @returns {Promise} 返回操作结果
   */
  changePassword: (data) => {
    return window.$http.put('/api/user/password', data)
  },

  /**
   * 获取用户收藏列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.type] - 收藏类型
   * @returns {Promise} 返回收藏列表
   */
  getCollectionList: (params) => {
    return window.$http.fetch('/api/user/collections', params)
  },

  /**
   * 获取用户申报记录
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回申报记录列表
   */
  getApplicationRecords: (params) => {
    return window.$http.fetch('/api/user/applications', params)
  },

  /**
   * 上传头像
   * @param {FormData} formData - 头像文件
   * @returns {Promise} 返回上传结果
   */
  uploadAvatar: (formData) => {
    return window.$http.post('/api/user/avatar', formData)
  },

  /**
   * 发送验证码
   * @param {Object} data - 验证码数据
   * @param {string} data.phone - 手机号
   * @param {string} data.type - 验证码类型
   * @returns {Promise} 返回操作结果
   */
  sendVerifyCode: (data) => {
    return window.$http.post('/api/auth/verify-code', data)
  },

  /**
   * 手机号注册
   * @param {Object} data - 注册数据
   * @param {string} data.phone - 手机号
   * @param {string} data.verifyCode - 验证码
   * @param {string} data.password - 密码
   * @returns {Promise} 返回注册结果
   */
  register: (data) => {
    return window.$http.post('/api/auth/register', data)
  },

  /**
   * 忘记密码
   * @param {Object} data - 重置密码数据
   * @param {string} data.phone - 手机号
   * @param {string} data.verifyCode - 验证码
   * @param {string} data.newPassword - 新密码
   * @returns {Promise} 返回操作结果
   */
  resetPassword: (data) => {
    return window.$http.post('/api/auth/reset-password', data)
  }
}
