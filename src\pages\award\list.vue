<!-- 奖补公示页面 -->
<template>
  <div class="award-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索公示信息"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 公示列表 -->
    <div class="award-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="award in awardList"
            :key="award.id"
            class="award-item"
            @click="navigateToDetail(award.id)"
          >
            <div class="award-header">
              <h3 class="award-title">{{ award.title }}</h3>
              <van-tag 
                :type="getStatusType(award.status)" 
                size="mini"
              >
                {{ award.statusText }}
              </van-tag>
            </div>
            <div class="award-content">
              <div class="award-info">
                <div class="info-item">
                  <span class="label">公示期：</span>
                  <span class="value">{{ award.publicityPeriod }}</span>
                </div>
                <div class="info-item">
                  <span class="label">补贴金额：</span>
                  <span class="value amount">{{ formatAmount(award.amount) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">受益人数：</span>
                  <span class="value">{{ award.beneficiaryCount }}人</span>
                </div>
              </div>
            </div>
            <div class="award-footer">
              <span class="publish-time">{{ award.publishTime }}</span>
              <van-icon name="arrow" class="award-arrow" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && awardList.length === 0"
      description="暂无公示信息"
      image="search"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { awardApi } from '@/api/award'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const awardList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 导航到详情页
const navigateToDetail = (id) => {
  router.push(`/award/detail/${id}`)
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'publicity': 'warning',
    'completed': 'success',
    'expired': 'default'
  }
  return statusMap[status] || 'default'
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万元'
  }
  return amount + '元'
}

// 搜索
const onSearch = () => {
  resetList()
  loadAwardList()
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  resetList()
  loadAwardList()
}

// 重置列表
const resetList = () => {
  awardList.value = []
  currentPage.value = 1
  finished.value = false
}

// 下拉刷新
const onRefresh = () => {
  resetList()
  loadAwardList().finally(() => {
    refreshing.value = false
  })
}

// 上拉加载
const onLoad = () => {
  loadAwardList()
}

// 加载公示列表
const loadAwardList = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value
    }
    
    // Mock数据
    const mockData = {
      list: [
        {
          id: '1',
          title: '2024年第一批购房补贴公示',
          status: 'publicity',
          statusText: '公示中',
          publicityPeriod: '2024-01-15 至 2024-01-22',
          amount: 2500000,
          beneficiaryCount: 50,
          publishTime: '2024-01-15'
        },
        {
          id: '2',
          title: '2023年第四批人才购房补贴公示',
          status: 'completed',
          statusText: '已完成',
          publicityPeriod: '2023-12-20 至 2023-12-27',
          amount: 1800000,
          beneficiaryCount: 18,
          publishTime: '2023-12-20'
        },
        {
          id: '3',
          title: '2023年第三批青年人才购房补贴公示',
          status: 'completed',
          statusText: '已完成',
          publicityPeriod: '2023-11-15 至 2023-11-22',
          amount: 900000,
          beneficiaryCount: 30,
          publishTime: '2023-11-15'
        }
      ],
      total: 3
    }
    
    if (currentPage.value === 1) {
      awardList.value = mockData.list || []
    } else {
      awardList.value.push(...(mockData.list || []))
    }
    
    // 判断是否还有更多数据
    if (!mockData.list || mockData.list.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载公示列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAwardList()
})

onActivated(() => {
  // 页面激活时刷新数据
  if (awardList.value.length === 0) {
    loadAwardList()
  }
})
</script>

<style lang="scss" scoped>
.award-list-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 16px;
  border-bottom: 1px solid var(--van-border-color);
}

.award-list {
  padding: 16px;
}

.award-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
}

.award-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.award-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.award-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin: 0;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.award-content {
  margin-bottom: 12px;
}

.award-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-size: 14px;
  color: var(--van-text-color-2);
  width: 80px;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: var(--van-text-color);
  flex: 1;
}

.value.amount {
  color: var(--van-primary-color);
  font-weight: 600;
}

.award-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid var(--van-border-color);
}

.publish-time {
  font-size: 12px;
  color: var(--van-text-color-3);
}

.award-arrow {
  color: var(--van-text-color-3);
  font-size: 14px;
}
</style>

<route lang="json5">
{
  name: 'AwardList',
  meta: {
    title: '奖补公示'
  }
}
</route>
