<template>
    <div>
        <FuniList ref="funiListRef" :tabs="tabsConfig" @item-click="handleItemClick">
            <template #item="{ item }">
                <div class="contract-item">
                    <div class="contract-header">
                        <div class="contract-status">
                            <van-tag v-if="item.isReported" type="primary" size="mini">已备案</van-tag>
                        </div>
                        <div class="contract-title">{{ item.projectName }}</div>
                        <van-radio :checked="selectedContract?.contractNumber === item.contractNumber"
                            @click.stop="handleContractSelect(item)" />
                    </div>
                    <div class="contract-info">
                        <div class="info-row">
                            <span class="label">合同编号</span>
                            <span class="value">{{ item.contractNumber }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">合同类型</span>
                            <span class="value">{{ item.contractType }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">房源区域</span>
                            <span class="value">{{ item.district }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">房屋唯一号</span>
                            <span class="value">{{ item.houseNumber }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">建筑面积</span>
                            <span class="value">{{ item.buildingArea }}㎡</span>
                        </div>
                    </div>
                    <div v-if="item.isNotReported" class="not-reported-tag">
                        <van-tag type="danger" size="mini">不满足网签</van-tag>
                    </div>
                </div>
            </template>
        </FuniList>
    </div>
</template>

<script setup>
import { ref } from 'vue';


const funiListRef = ref()

const tabsConfig = [
    {
        key: 'one', title: '选择合同', loadFunction: (params) => {
            return {
                data: [{
                    houseAddress: "aaaaa"
                }],
                total: 1
            }
        }
    }
]
</script>

<style lang="scss" scoped>
.contract-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    position: relative;

    .contract-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 12px;

        .contract-status {
            margin-bottom: 8px;
        }

        .contract-title {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
            color: var(--van-text-color);
            margin-right: 12px;
        }
    }

    .contract-info {
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--van-border-color);

            &:last-child {
                border-bottom: none;
            }

            .label {
                color: var(--van-text-color-2);
                font-size: 14px;
                width: 80px;
                flex-shrink: 0;
            }

            .value {
                color: var(--van-text-color);
                font-size: 14px;
                text-align: right;
                flex: 1;
            }
        }
    }

    .not-reported-tag {
        position: absolute;
        top: 16px;
        right: 16px;
    }

    &.unqualified {
        opacity: 0.6;
    }
}
</style>