<!-- 政策列表页面 -->
<template>
  <div class="policy-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索政策"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 分类筛选 -->
    <div class="category-section">
      <van-tabs v-model:active="activeCategory" @change="onCategoryChange">
        <van-tab title="全部" name="all" />
        <van-tab title="购房补贴" name="subsidy" />
        <van-tab title="人才政策" name="talent" />
        <van-tab title="优惠政策" name="discount" />
      </van-tabs>
    </div>

    <!-- 政策列表 -->
    <div class="policy-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="policy in policyList"
            :key="policy.id"
            class="policy-item"
            @click="navigateToDetail(policy.id)"
          >
            <div class="policy-header">
              <h3 class="policy-title">{{ policy.title }}</h3>
              <van-tag v-if="policy.isHot" type="danger" size="mini">热门</van-tag>
            </div>
            <p class="policy-summary">{{ policy.summary }}</p>
            <div class="policy-footer">
              <div class="policy-info">
                <span class="policy-amount">{{ policy.amount }}</span>
                <span class="policy-time">{{ policy.publishTime }}</span>
              </div>
              <van-icon name="arrow" class="policy-arrow" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && policyList.length === 0"
      description="暂无政策信息"
      image="search"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { policyApi } from '@/api/policy'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('all')
const policyList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 导航到详情页
const navigateToDetail = (id) => {
  router.push(`/policy/detail/${id}`)
}

// 搜索
const onSearch = () => {
  resetList()
  loadPolicyList()
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  resetList()
  loadPolicyList()
}

// 分类切换
const onCategoryChange = () => {
  resetList()
  loadPolicyList()
}

// 重置列表
const resetList = () => {
  policyList.value = []
  currentPage.value = 1
  finished.value = false
}

// 下拉刷新
const onRefresh = () => {
  resetList()
  loadPolicyList().finally(() => {
    refreshing.value = false
  })
}

// 上拉加载
const onLoad = () => {
  loadPolicyList()
}

// 加载政策列表
const loadPolicyList = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value,
      category: activeCategory.value === 'all' ? '' : activeCategory.value
    }
    
    const response = await policyApi.getPolicyList(params)
    
    if (currentPage.value === 1) {
      policyList.value = response.list || []
    } else {
      policyList.value.push(...(response.list || []))
    }
    
    // 判断是否还有更多数据
    if (!response.list || response.list.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载政策列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadPolicyList()
})

onActivated(() => {
  // 页面激活时刷新数据
  if (policyList.value.length === 0) {
    loadPolicyList()
  }
})
</script>

<style lang="scss" scoped>
.policy-list-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 16px;
}

.category-section {
  background: white;
  border-bottom: 1px solid var(--van-border-color);
}

.policy-list {
  padding: 16px;
}

.policy-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
}

.policy-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.policy-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin: 0;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.policy-summary {
  font-size: 14px;
  color: var(--van-text-color-2);
  line-height: 1.5;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.policy-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.policy-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.policy-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-primary-color);
}

.policy-time {
  font-size: 12px;
  color: var(--van-text-color-3);
}

.policy-arrow {
  color: var(--van-text-color-3);
  font-size: 14px;
}
</style>

<route lang="json5">
{
  name: 'PolicyList',
  meta: {
    title: '热门政策'
  }
}
</route>
