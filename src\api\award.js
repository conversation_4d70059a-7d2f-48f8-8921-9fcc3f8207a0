/**
 * 奖补公示相关API接口
 */

export const awardApi = {
  /**
   * 获取奖补公示列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.keyword] - 搜索关键词
   * @param {number} [params.categoryId] - 分类ID
   * @returns {Promise} 返回公示列表
   */
  getNoticeList: (params) => {
    return window.$http.fetch('/api/awards/notices', params)
  },

  /**
   * 获取奖补公示详情
   * @param {string} id - 公示ID
   * @returns {Promise} 返回公示详情
   */
  getNoticeDetail: (id) => {
    return window.$http.fetch(`/api/awards/notices/${id}`)
  },

  /**
   * 收藏/取消收藏公示
   * @param {string} id - 公示ID
   * @param {boolean} isCollect - 是否收藏
   * @returns {Promise} 返回操作结果
   */
  toggleCollectNotice: (id, isCollect) => {
    return window.$http.post(`/api/awards/notices/${id}/collect`, { isCollect })
  },

  /**
   * 获取奖补公示分类列表
   * @returns {Promise} 返回分类列表
   */
  getNoticeCategories: () => {
    return window.$http.fetch('/api/awards/categories')
  },

  /**
   * 获取相关公示推荐
   * @param {string} id - 当前公示ID
   * @returns {Promise} 返回相关公示列表
   */
  getRelatedNotices: (id) => {
    return window.$http.fetch(`/api/awards/notices/${id}/related`)
  },

  /**
   * 举报问题
   * @param {Object} data - 举报数据
   * @param {string} data.noticeId - 公示ID
   * @param {string} data.content - 举报内容
   * @param {string} data.contact - 联系方式
   * @returns {Promise} 返回操作结果
   */
  reportIssue: (data) => {
    return window.$http.post('/api/awards/reports', data)
  }
}
