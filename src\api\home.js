/**
 * 首页相关API接口
 */

export const homeApi = {
  /**
   * 获取首页轮播图
   * @returns {Promise} 返回轮播图列表
   */
  getBannerList: () => {
    // Mock数据
    return Promise.resolve({
      list: [
        {
          id: '1',
          title: '购房补贴政策解读',
          image: '/images/banner1.jpg',
          link: '/policy/detail/1'
        },
        {
          id: '2',
          title: '申请流程指南',
          image: '/images/banner2.jpg',
          link: '/guide/detail/1'
        }
      ]
    })
  },

  /**
   * 获取最新公告列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回公告列表数据
   */
  getNoticeList: (params) => {
    // Mock数据
    return Promise.resolve({
      list: [
        {
          id: '1',
          title: '关于2024年购房补贴申请的通知',
          summary: '为进一步支持居民购房需求，现开放2024年度购房补贴申请...',
          publishTime: '2024-01-15',
          isTop: true
        },
        {
          id: '2',
          title: '购房补贴政策调整公告',
          summary: '根据最新政策要求，对购房补贴申请条件进行适当调整...',
          publishTime: '2024-01-10',
          isTop: false
        },
        {
          id: '3',
          title: '系统维护通知',
          summary: '为提升系统服务质量，将于本周末进行系统维护升级...',
          publishTime: '2024-01-08',
          isTop: false
        }
      ],
      total: 3,
      current: params.current || 1,
      size: params.size || 10
    })
  },

  /**
   * 获取公告详情
   * @param {string} id - 公告ID
   * @returns {Promise} 返回公告详情
   */
  getNoticeDetail: (id) => {
    // Mock数据
    return Promise.resolve({
      id,
      title: '关于2024年购房补贴申请的通知',
      content: `
        <p>为进一步支持居民购房需求，促进房地产市场健康发展，现开放2024年度购房补贴申请。</p>
        <h3>申请条件：</h3>
        <ul>
          <li>在本市首次购买商品住房</li>
          <li>购房时间在2024年1月1日之后</li>
          <li>申请人具有本市户籍或连续缴纳社保满2年</li>
        </ul>
        <h3>补贴标准：</h3>
        <ul>
          <li>普通购房者：最高5万元</li>
          <li>高层次人才：最高10万元</li>
          <li>青年人才：最高3万元</li>
        </ul>
      `,
      publishTime: '2024-01-15',
      author: '市住建局',
      viewCount: 1256
    })
  }
}
