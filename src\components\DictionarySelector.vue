<!-- 公共字典选择器组件 -->
<template>
  <van-field
    v-if="isFormStyle"
    v-model="displayText"
    :disabled="disabled"
    :required="required"
    :label="label"
    :placeholder="placeholder"
    readonly
    :clickable="!disabled && !loading"
    :rules="rules"
    @click="!disabled && !loading && openPicker()"
  >
    <template #right-icon>
      <van-loading v-if="loading" type="spinner" size="16px" />
      <van-icon v-else name="arrow-down" />
    </template>
  </van-field>
  <div
    v-else
    class="dropdown-selector"
    :class="{ 'dropdown-disabled': disabled, 'dropdown-loading': loading }"
    @click="!disabled && !loading && openPicker()"
  >
    <span class="dropdown-text" :class="{ 'dropdown-placeholder': !displayText }">
      {{ displayText || placeholder }}
    </span>
    <van-loading v-if="loading" type="spinner" size="14px" class="dropdown-icon" />
    <van-icon v-else name="arrow-down" class="dropdown-icon" />
  </div>
  <!-- 字典选择弹窗 -->
  <van-popup v-model:show="showPicker" round position="bottom">
    <van-picker
      :columns="options"
      @confirm="onConfirm"
      @cancel="showPicker = false"
      :title="pickerTitle"
    />
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { showToast } from "vant";
import { getDictionaryData } from "@/api/farm-plan";

defineOptions({
  name: "DictionarySelector",
});

const props = defineProps({
  // 绑定值
  modelValue: {
    type: [String, Object],
    default: "",
  },
  // 字典类型代码
  typeCode: {
    type: String,
    required: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false,
  },
  // 标签文本
  label: {
    type: String,
    default: "请选择",
  },
  // 占位符
  placeholder: {
    type: String,
    default: "请选择",
  },
  // 选择器标题
  pickerTitle: {
    type: String,
    default: "请选择",
  },
  // 验证规则
  rules: {
    type: Array,
    default: () => [],
  },
  // 返回值类型 'value' | 'object'
  valueType: {
    type: String,
    default: "value",
    validator: (value) => ["value", "object"].includes(value),
  },
  // 是否使用表单样式
  isFormStyle: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(["update:modelValue", "change"]);

// 响应式数据
const loading = ref(false);
const showPicker = ref(false);
const options = ref([]);
const selectedOption = ref(null);

// 计算属性
const displayText = computed(() => {
  if (props.valueType === "object" && props.modelValue?.text) {
    return props.modelValue.text;
  } else if (props.valueType === "value" && props.modelValue) {
    const option = options.value.find(
      (item) => item.value === props.modelValue
    );
    return option ? option.text : "";
  }
  return "";
});

// 方法
const openPicker = () => {
  if (options.value.length === 0) {
    fetchDictionaryData();
  }
  showPicker.value = true;
};

const fetchDictionaryData = async () => {
  if (!props.typeCode) {
    showToast("缺少字典类型代码");
    return;
  }

  try {
    loading.value = true;
    const response = await getDictionaryData({
      code: props.typeCode,
    });
    options.value = response.list.map((item) => ({
      text: item.name,
      value: item.code,
    }));
  } catch (error) {
    console.error("获取字典数据失败:", error);
    showToast("获取字典数据失败");
  } finally {
    loading.value = false;
  }
};

const onConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  selectedOption.value = option;

  if (props.valueType === "object") {
    emits("update:modelValue", {
      text: option.text,
      value: option.value,
    });
    emits("change", {
      text: option.text,
      value: option.value,
    });
  } else {
    emits("update:modelValue", option.value);
    emits("change", {
      text: option.text,
      value: option.value,
    });
  }

  showPicker.value = false;
};

// 监听 typeCode 变化，重新获取数据
watch(
  () => props.typeCode,
  (newTypeCode) => {
    if (newTypeCode) {
      options.value = [];
      fetchDictionaryData();
    }
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  if (props.typeCode) {
    fetchDictionaryData();
  }
});
</script>

<style lang="scss" scoped>
.dropdown-selector {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid #ebedf0;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  font-size: 12px;

  &:active {
    background: #fff;
  }

  &.dropdown-disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background: #f7f8fa;
  }

  &.dropdown-loading {
    cursor: default;
  }

  .dropdown-text {
    flex: 1;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.dropdown-placeholder {
      color: #000;
    }
  }

  .dropdown-icon {
    margin-left: 8px;
    color: #969799;
    flex-shrink: 0;
    transition: transform 0.2s ease;
  }

  // 点击时图标旋转效果（可选）
  &:active .dropdown-icon {
    transform: rotate(180deg);
  }
}
</style>
