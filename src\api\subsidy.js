/**
 * 补贴申请相关API接口
 */

export const subsidyApi = {
  /**
   * 获取符合条件的合同列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回符合条件的合同列表
   */
  getQualifiedContracts: (params) => {
    // Mock数据
    return Promise.resolve({
      list: [
        {
          contractNumber: 'FY23456784567890',
          projectName: '安徽省阜阳市XX街道革命花园',
          contractType: '商品房合同',
          district: '颍州区',
          houseNumber: '109876',
          buildingArea: '120.88',
          isReported: true
        },
        {
          contractNumber: 'FY23456784567891',
          projectName: '安徽省阜阳市XX街道革命花园',
          contractType: '商品房合同',
          district: '颍州区',
          houseNumber: '109877',
          buildingArea: '120.88',
          isReported: false
        }
      ],
      total: 2
    })
  },

  /**
   * 获取不符合条件的合同列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回不符合条件的合同列表
   */
  getUnqualifiedContracts: (params) => {
    // Mock数据
    return Promise.resolve({
      list: [
        {
          contractNumber: 'FY23456784567892',
          projectName: '安徽省阜阳市XX街道革命花园',
          contractType: '商品房合同',
          district: '颍州区',
          houseNumber: '109878',
          buildingArea: '120.88',
          isNotReported: true
        }
      ],
      total: 1
    })
  },

  /**
   * 根据合同编号获取合同信息
   * @param {string} contractNumber - 合同编号
   * @returns {Promise} 返回合同信息
   */
  getContractInfo: (contractNumber) => {
    // Mock数据
    return Promise.resolve({
      buyerName: '**民',
      buyerIdCard: '3345**************5555',
      contractNumber: 'FY23456784567',
      signDate: '2024.03.04 12:00:00',
      houseLocation: '颍州区中心街2号祥生云境小区GC1-住宅楼2204室',
      contractAmount: '1098765.00',
      propertyLocation: '颍州区中心街2号祥生云境小区GC1-住宅楼2204室',
      propertyNumber: '皖（2025）阜阳市不动产权第8268288号',
      registerDate: '2024.03.04 12:00:00',
      propertyUnitNumber: '341202003004GB00051F0003',
      taxRegistrationNumber: '201134010003277202',
      taxAmount: '4569.09',
      taxIssueDate: '2024.03.04 12:00:00',
      taxPropertyInfo: '房源编号:F341202202500240045 房屋坐落位置:祥生云境小区GC1-住宅楼2204室 房屋面积:124.45平米 合同签订时间:2023-03-15'
    })
  },

  /**
   * 提交补贴申请
   * @param {Object} data - 申请数据
   * @param {string} data.contractNumber - 合同编号
   * @param {string} data.contractDate - 签约日期
   * @param {Object} data.accountInfo - 收款账户信息
   * @returns {Promise} 返回申请结果
   */
  submitApplication: (data) => {
    return window.$http.post('/api/subsidy/applications', data)
  },

  /**
   * 获取申请列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.status] - 申请状态
   * @returns {Promise} 返回申请列表
   */
  getApplicationList: (params) => {
    return window.$http.fetch('/api/subsidy/applications', params)
  },

  /**
   * 获取申请详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回申请详情
   */
  getApplicationDetail: (id) => {
    return window.$http.fetch(`/api/subsidy/applications/${id}`)
  },

  /**
   * 撤销申请
   * @param {string} id - 申请ID
   * @returns {Promise} 返回操作结果
   */
  cancelApplication: (id) => {
    return window.$http.put(`/api/subsidy/applications/${id}/cancel`)
  },

  /**
   * 修改申请
   * @param {string} id - 申请ID
   * @param {Object} data - 修改数据
   * @returns {Promise} 返回操作结果
   */
  modifyApplication: (id, data) => {
    return window.$http.put(`/api/subsidy/applications/${id}`, data)
  }
}
