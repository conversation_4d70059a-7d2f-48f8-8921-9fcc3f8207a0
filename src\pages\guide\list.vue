<!-- 申报指南页面 -->
<template>
  <div class="guide-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索申报指南"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 分类筛选 -->
    <div class="category-section">
      <van-tabs v-model:active="activeCategory" @change="onCategoryChange">
        <van-tab title="全部" name="all" />
        <van-tab title="申请流程" name="process" />
        <van-tab title="材料清单" name="materials" />
        <van-tab title="常见问题" name="faq" />
      </van-tabs>
    </div>

    <!-- 指南列表 -->
    <div class="guide-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="guide in guideList"
            :key="guide.id"
            class="guide-item"
            @click="navigateToDetail(guide.id)"
          >
            <div class="guide-header">
              <h3 class="guide-title">{{ guide.title }}</h3>
              <van-tag v-if="guide.isRecommend" type="primary" size="mini">推荐</van-tag>
            </div>
            <p class="guide-summary">{{ guide.summary }}</p>
            <div class="guide-footer">
              <div class="guide-meta">
                <span class="update-time">{{ guide.updateTime }}</span>
                <span class="view-count">
                  <van-icon name="eye-o" />
                  {{ guide.viewCount }}
                </span>
              </div>
              <van-icon name="arrow" class="guide-arrow" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && guideList.length === 0"
      description="暂无申报指南"
      image="search"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { guideApi } from '@/api/guide'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('all')
const guideList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 导航到详情页
const navigateToDetail = (id) => {
  router.push(`/guide/detail/${id}`)
}

// 搜索
const onSearch = () => {
  resetList()
  loadGuideList()
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  resetList()
  loadGuideList()
}

// 分类切换
const onCategoryChange = () => {
  resetList()
  loadGuideList()
}

// 重置列表
const resetList = () => {
  guideList.value = []
  currentPage.value = 1
  finished.value = false
}

// 下拉刷新
const onRefresh = () => {
  resetList()
  loadGuideList().finally(() => {
    refreshing.value = false
  })
}

// 上拉加载
const onLoad = () => {
  loadGuideList()
}

// 加载指南列表
const loadGuideList = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value,
      category: activeCategory.value === 'all' ? '' : activeCategory.value
    }
    
    // Mock数据
    const mockData = {
      list: [
        {
          id: '1',
          title: '购房补贴申请完整流程指南',
          summary: '从申请条件到材料准备，从提交申请到审核流程，全面解读购房补贴申请的每个环节',
          updateTime: '2024-01-15',
          viewCount: 1250,
          isRecommend: true,
          category: 'process'
        },
        {
          id: '2',
          title: '申请材料准备清单及注意事项',
          summary: '详细列出申请购房补贴所需的各类材料，包括格式要求、有效期限等重要信息',
          updateTime: '2024-01-10',
          viewCount: 980,
          isRecommend: true,
          category: 'materials'
        },
        {
          id: '3',
          title: '购房补贴申请常见问题解答',
          summary: '汇总申请过程中最常遇到的问题及官方解答，帮助申请人快速解决疑问',
          updateTime: '2024-01-08',
          viewCount: 756,
          isRecommend: false,
          category: 'faq'
        },
        {
          id: '4',
          title: '不同类型人才的补贴标准说明',
          summary: '详细说明普通购房者、高层次人才、青年人才等不同群体的补贴标准和申请条件',
          updateTime: '2024-01-05',
          viewCount: 642,
          isRecommend: false,
          category: 'process'
        },
        {
          id: '5',
          title: '银行账户信息填写规范',
          summary: '指导如何正确填写收款银行账户信息，避免因信息错误导致补贴发放延误',
          updateTime: '2024-01-03',
          viewCount: 523,
          isRecommend: false,
          category: 'materials'
        }
      ],
      total: 5
    }
    
    // 根据分类筛选
    let filteredList = mockData.list
    if (activeCategory.value !== 'all') {
      filteredList = mockData.list.filter(item => item.category === activeCategory.value)
    }
    
    // 根据关键词筛选
    if (searchKeyword.value) {
      filteredList = filteredList.filter(item => 
        item.title.includes(searchKeyword.value) || 
        item.summary.includes(searchKeyword.value)
      )
    }
    
    if (currentPage.value === 1) {
      guideList.value = filteredList || []
    } else {
      guideList.value.push(...(filteredList || []))
    }
    
    // 判断是否还有更多数据
    if (!filteredList || filteredList.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载指南列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadGuideList()
})

onActivated(() => {
  // 页面激活时刷新数据
  if (guideList.value.length === 0) {
    loadGuideList()
  }
})
</script>

<style lang="scss" scoped>
.guide-list-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 16px;
}

.category-section {
  background: white;
  border-bottom: 1px solid var(--van-border-color);
}

.guide-list {
  padding: 16px;
}

.guide-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
}

.guide-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.guide-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin: 0;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.guide-summary {
  font-size: 14px;
  color: var(--van-text-color-2);
  line-height: 1.5;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.guide-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guide-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.update-time {
  font-size: 12px;
  color: var(--van-text-color-3);
}

.view-count {
  font-size: 12px;
  color: var(--van-text-color-3);
  display: flex;
  align-items: center;
  gap: 4px;
}

.guide-arrow {
  color: var(--van-text-color-3);
  font-size: 14px;
}
</style>

<route lang="json5">
{
  name: 'GuideList',
  meta: {
    title: '申报指南'
  }
}
</route>
