<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import homeSvg from '@/assets/home/<USER>'
import progressSvg from '@/assets/home/<USER>'
import mineSvg from '@/assets/home/<USER>'

const route = useRoute()
const router = useRouter()

// 底部导航配置
const tabbarActive = ref(0)
const tabbarList = [
  { name: 'Home', label: '首页', icon: homeSvg },
  { name: 'Progress', label: '进度', icon: progressSvg },
  { name: 'Mine', label: '我的', icon: mineSvg }
]

// 监听路由变化更新tabbar状态
const updateTabbarActive = () => {
  const routeName = route.name
  const index = tabbarList.findIndex(item => item.name === routeName)
  if (index !== -1) {
    tabbarActive.value = index
  }
}

// 切换tabbar
const onTabbarChange = (index) => {
  const targetRoute = tabbarList[index].name
  if (route.name !== targetRoute) {
    router.push({ name: targetRoute })
  }
}

// 监听路由变化
watch(() => route.name, updateTabbarActive, { immediate: true })
</script>

<template>
  <div class="app-container">
    <nav-bar />
    <router-view v-slot="{ Component }">
      <section class="app-wrapper">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </section>
    </router-view>

    <!-- 底部导航栏 -->
    <van-tabbar v-model="tabbarActive" @change="onTabbarChange" placeholder safe-area-inset-bottom>
      <van-tabbar-item v-for="(item, index) in tabbarList" :key="index" :icon="item.icon">

        <span>{{ item.label }}</span>
        <template #icon="props">
          <component :is="item.icon" :style="{ color: props.active ? '#FE5000' : '#C2C2C2' }"></component>
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style lang="scss" scoped>
.app-container {
  --van-tabbar-height: 60px;

  ::v-deep() {
    .van-tabbar {
      border-radius: 12px 12px 0 0;
      overflow: hidden;
    }
  }
}

.app-wrapper {
  width: 100%;
  flex: 1;
}
</style>
