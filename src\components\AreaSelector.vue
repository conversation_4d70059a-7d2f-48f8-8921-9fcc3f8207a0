<!-- 公共区域选择组件 -->
<template>
  <div class="area-selector" :class="customClass">
    <!-- 表单样式 -->
    <van-field
      v-if="isFormStyle"
      v-model="areaText"
      :disabled="disabled"
      :required="required"
      :label="label"
      :placeholder="placeholder"
      readonly
      :clickable="!disabled && !loading"
      :rules="rules"
      @click="!disabled && !loading && openAreaPicker()"
    >
      <template #right-icon>
        <van-loading v-if="loading" type="spinner" size="16px" />
        <van-icon v-else name="arrow-down" />
      </template>
    </van-field>

    <!-- 下拉样式 -->
    <div
      v-else
      class="dropdown-selector"
      :class="{ 'dropdown-disabled': disabled, 'dropdown-loading': loading }"
      @click="!disabled && !loading && openAreaPicker()"
    >
      <span
        class="dropdown-text"
        :class="{ 'dropdown-placeholder': !areaText }"
      >
        {{ areaText || placeholder }}
      </span>
      <van-loading
        v-if="loading"
        type="spinner"
        size="14px"
        class="dropdown-icon"
      />
      <van-icon v-else name="arrow-down" class="dropdown-icon" />
    </div>

    <!-- 区域选择弹窗 -->
    <van-popup
      class="area-popup"
      v-model:show="showPicker"
      round
      position="bottom"
      :lazy-render="false"
      teleport="body"
    >
      <van-cascader
        v-model="selectedCodes"
        title="请选择地区"
        :options="areaOptions"
        :field-names="fieldNames"
        :closeable="false"
        @change="onAreaChange"
      >
        <template #title>
          <div class="title-box">
            <div class="cancel" @click="showPicker = false">取消</div>
            <div>请选择地区</div>
            <div class="ok" @click="onAreaFinish">确认</div>
          </div>
        </template>
      </van-cascader>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { showLoadingToast, closeToast, showToast } from "vant";

defineOptions({
  name: "AreaSelector",
});

const props = defineProps({
  // 绑定值
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false,
  },
  // 标签文本
  label: {
    type: String,
    default: "所在地区",
  },
  // 占位符
  placeholder: {
    type: String,
    default: "请选择地区",
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: "",
  },
  // 最大级别 (1: 省, 2: 市, 3: 区, 4: 街道, 5: 社区)
  maxLevel: {
    type: Number,
    default: 5,
    validator: (value) => [1, 2, 3, 4, 5].includes(value),
  },
  // 级联层级 (3: 省市区, 4: 省市区县, 5: 省市区县街道)
  level: {
    type: Number,
    default: 3,
  },
  // 验证规则
  rules: {
    type: Array,
    default: () => [],
  },
  // 是否使用表单样式
  isFormStyle: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(["update:modelValue", "change"]);

// 响应式数据
const loading = ref(false);
const showPicker = ref(false);
const areaOptions = ref([]);
const selectedCodes = ref([]);

let selectedRegions = [];
// 字段映射
const fieldNames = {
  text: "districtName",
  value: "districtCode",
  children: "children",
};

// 计算属性
const areaText = computed(() => {
  const { province, city, district, street,community } = props.modelValue;
  const areas = [];

  if (province?.label) areas.push(province.label);
  if (city?.label) areas.push(city.label);
  if (district?.label) areas.push(district.label);
  if (props.maxLevel >= 4 && street?.label) areas.push(street.label);
  if (props.maxLevel >= 5 && community?.label) areas.push(community.label);

  return areas.join("/");
});

// 方法
const openAreaPicker = () => {
  showPicker.value = true;
  initSelectedCodes();
};

const closePicker = () => {
  showPicker.value = false;
};

const initSelectedCodes = () => {
  const codes = [];
  const { province, city, district,street, community  } = props.modelValue;
  if (province?.value) codes.push(province.value);
  if (city?.value) codes.push(city.value);
  if (district?.value) codes.push(district.value);
  if (props.maxLevel >= 4 && street?.value) codes.push(street.value);
  if (props.maxLevel >= 5 && community?.value) codes.push(community.value);

  selectedCodes.value = codes.at(-1);
};

// 获取区域数据
const fetchAreaData = async () => {
  try {
    loading.value = true;
    const response = await window.$http.fetch(
      "/csccs/district/findDistrictByLevel",
      { level: 0 }
    );
    response.forEach((item) => {
      item.children = [];
    });
    areaOptions.value = response || [];
  } catch (error) {
    console.error("获取区域数据失败:", error);
    showToast("获取区域数据失败");
  } finally {
    loading.value = false;
  }
};

// 获取子级区域数据
const fetchChildrenData = async (parentCode) => {
  try {
    const response = await window.$http.post(
      "/csccs/district/findDistrictByIdOrCode",
      {
        districtCode: parentCode,
      }
    );
    return response || [];
  } catch (error) {
    console.error("获取子级区域数据失败:", error);
    return [];
  }
};

// 区域选择变化
const onAreaChange = async ({ value, selectedOptions }) => {
  try {
    selectedRegions = selectedOptions;
    // 如果已经选择到最大层级，直接完成选择
    if (selectedOptions.length >= props.maxLevel) {
      return;
    }
    // 显示加载状态
    showLoadingToast({
      message: "加载中...",
      duration: 0,
    });
    // 获取子级数据
    const children = await fetchChildrenData(value);
    if (children.length > 0) {
      // 添加子级数据到当前选项
      const lastOption = selectedOptions[selectedOptions.length - 1];
      if (selectedOptions.length < props.maxLevel - 1) {
        children.forEach((item) => {
          item.children = item.children || [];
        });
      }
      lastOption.children = children;
      closeToast();
    }
  } catch (error) {
    closeToast();
    showToast("加载区域数据失败");
  }
};

// 完成区域选择
const onAreaFinish = async () => {
  if (selectedRegions.length < props.level) {
    const tip = {
      1: "省",
      2: "市",
      3: "区",
      4: "街道",
      5: "社区/委员会",
    };
    let tipPrefix = props.level != props.maxLevel ? "请最少选择到" : "请选择到";
    showToast(tipPrefix + tip[props.level]);
    return;
  }
  const newValue = { ...props.modelValue };
  const keys = ["province", "city", "district", "street", "community"];

  // 清空所有区域数据
  keys.forEach((key) => {
    newValue[key] = undefined;
  });

  // 设置新的区域数据
  selectedRegions.forEach((option, index) => {
    if (keys[index]) {
      newValue[keys[index]] = {
        label: option.districtName,
        value: option.districtCode,
      };
    }
  });

  emits("update:modelValue", newValue);
  emits("change", newValue);
  closePicker();
};

// 初始化已选区域的完整数据
const initAreaData = async () => {
  if (!props.modelValue.province?.value) return;

  try {
    loading.value = true;
    const promises = [];
    const keys = ["province", "city", "district", "street",'community'];

    // 构建请求队列
    for (let i = 0; i < Math.min(props.maxLevel - 1, keys.length); i++) {
      const key = keys[i];
      if (props.modelValue[key]?.value) {
        promises.push(fetchChildrenData(props.modelValue[key].value));
      } else {
        break;
      }
    }

    if (promises.length === 0) return;

    const childrenList = await Promise.all(promises);

    // 构建树形结构
    let currentLevel = areaOptions.value;

    childrenList.forEach((children, index) => {
      const key = keys[index];
      const targetCode = props.modelValue[key]?.value;
      children.forEach((item) => {
        item.children = item.children || [];
      });
      if (targetCode && currentLevel) {
        const targetNode = currentLevel.find(
          (item) => item.districtCode === targetCode
        );
        if (targetNode && children.length > 0) {
          targetNode.children = children;
          currentLevel = children;
        }
      }
    });
  } catch (error) {
    console.error("初始化区域数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue?.length > 0 && !areaOptions.value.length) {
      initAreaData();
    }
  },
  { deep: true, immediate: true }
);

// 生命周期
onMounted(() => {
  fetchAreaData();
});
</script>

<style lang="scss" scoped>
.area-selector {
  .dropdown-selector {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 12px;
    background: #fff;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    font-size: 12px;
    border-radius: 12px;
    &:hover {
      border-color: #c8c9cc;
      background: #f2f3f5;
    }

    &:active {
      background: #e5e5e5;
    }

    &.dropdown-disabled {
      cursor: not-allowed;
      opacity: 0.6;
      background: #f7f8fa;

      &:hover {
        border-color: #ebedf0;
        background: #f7f8fa;
      }
    }

    &.dropdown-loading {
      cursor: default;

      &:hover {
        border-color: #ebedf0;
        background: #f7f8fa;
      }
    }

    .dropdown-text {
      flex: 1;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.dropdown-placeholder {
        color: #000;
      }
    }

    .dropdown-icon {
      margin-left: 8px;
      color: #969799;
      flex-shrink: 0;
      transition: transform 0.2s ease;
    }

    // 点击时图标旋转效果
    &:active .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}
.area-popup {
  :deep(.title-box) {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    .cancel {
      color: var(--van-picker-cancel-action-color);
      font-size: 14px;
    }
    .ok {
      color: var(--van-picker-confirm-action-color);
      font-size: 14px;
    }
  }
  :deep(.van-cascader__title) {
    width: 100%;
  }
}
</style>
