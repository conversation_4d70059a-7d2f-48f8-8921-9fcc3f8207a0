<!-- 购房补贴首页 -->
<template>
  <div class="home-page">
    <!-- 头部横幅区域 -->
    <div class="hero-section"></div>

    <!-- 功能模块区域 -->
    <div class="modules-section">
      <div class="modules-grid">
        <!-- 补贴申请 -->
        <div class="module-item" @click="navigateTo('/subsidy/apply')" style="background-color: rgba(254, 80, 0, 0.08);">
          <img src="@/assets/home/<USER>">
          <span>补贴申请</span>
        </div>

        <!-- 热门政策 -->
        <div class="module-item" @click="navigateTo('/policy/list')" style="background-color: rgba(253, 170, 10, 0.08)">
          <img src="@/assets/home/<USER>">
          <span>热门政策</span>

        </div>

        <!-- 奖补公示 -->
        <div class="module-item" @click="navigateTo('/award/list')" style="background-color: rgba(104, 94, 254, 0.08)">
          <img src="@/assets/home/<USER>">
          <span>奖补公示</span>
        </div>

        <!-- 申报指南 -->
        <div class="module-item" @click="navigateTo('/guide/list')" style="background-color: rgba(22, 205, 182, 0.08)">
          <img src="@/assets/home/<USER>">
          <span>申报指南</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据


// 导航方法
const navigateTo = (path) => {
  router.push(path)
}

</script>

<style lang="scss" scoped>
.home-page{
  height: 100%;
  display: flex;
  flex-direction: column;
}
// 头部横幅区域
.hero-section {
  height: 211px;
  background: url(@/assets/home/<USER>
  background-size: 100% 100%;
}


// 功能模块区域
.modules-section {
  flex:1;
  padding: 20px;
  background: white;
  margin-top: -20px;
  margin-bottom: 16px;
  border-radius: 20px 20px 0 0;
  position: relative;
  z-index: 3;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.module-item {
  display: flex;
  align-items: center;
  padding: 17px 6px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;

  img {
    width: 56px;
    height: 56px;
    margin-right: 6px;
  }
}


</style>

<route lang="json5">
{
  name: 'Home',
  meta: {
    title: '购房补贴'
  }
}
</route>
