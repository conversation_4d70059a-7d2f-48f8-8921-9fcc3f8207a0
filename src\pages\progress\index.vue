<!-- 进度查询页面 -->
<template>
  <div class="progress-page">
    <FuniList ref="progressListRef" :tabs="progressTabs" @item-click="handleItemClick">

      <!-- 进行中的补贴申请提示 -->
      <template #item="{ item }">
        <div class="application-item">
          <div class="application-content">
            <div class="info-row">
              <span class="label">房源地址</span>
              <span class="value">{{ item.houseAddress }}</span>
            </div>
            <div class="info-row">
              <span class="label">合同编号</span>
              <span class="value">{{ item.contractNumber }}</span>
            </div>
            <div class="info-row">
              <span class="label">合同类型</span>
              <span class="value">{{ item.contractType }}</span>
            </div>
            <div class="info-row">
              <span class="label">房源区域</span>
              <span class="value">{{ item.district }}</span>
            </div>
            <div class="info-row">
              <span class="label">房屋唯一号</span>
              <span class="value">{{ item.houseNumber }}</span>
            </div>
            <div class="info-row">
              <span class="label">建筑面积</span>
              <span class="value">{{ item.buildingArea }}㎡</span>
            </div>
          </div>

          <div class="application-actions">
            <van-button plain type="primary" size="small" @click.stop="handleApplicationDetail(item)">
              申请详情
            </van-button>
            <van-button type="primary" size="small" @click.stop="handleProcessProgress(item)">
              办理进度
            </van-button>
          </div>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { progressApi } from '@/api/progress'
import FuniList from '@/components/FuniList.vue'

const router = useRouter()

// 响应式数据
const progressListRef = ref()

// 进度列表配置
const progressTabs = [
  {
    key: 'all', title: '全部', showFilter: false, searchPlaceholder: "输入合同号，房屋地址", loadFunction: (params) => {
      return {
        data:[{
          houseAddress:"aaaaa"
        }],
        total:1
      }
    }
  }
]


// 列表项点击
const handleItemClick = (item) => {
  console.log('点击项目:', item)
}

// 申请详情
const handleApplicationDetail = (item) => {
  router.push(`/progress/detail/${item.applicationNumber}`)
}

// 办理进度
const handleProcessProgress = (item) => {
  router.push(`/progress/process/${item.applicationNumber}`)
}


</script>

<style lang="scss" scoped>
.progress-page {
  height: 100%;
}

.application-header {
  margin-bottom: 16px;
}

.application-info {
  .application-title {
    font-size: 14px;
    color: var(--van-text-color-2);
    margin-bottom: 8px;
  }

  .application-number {
    display: flex;
    align-items: center;
    gap: 8px;

    .label {
      font-size: 14px;
      color: var(--van-text-color-2);
    }

    .number {
      font-size: 14px;
      color: var(--van-text-color);
      font-weight: 500;
    }
  }
}

.application-content {
  margin-bottom: 16px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--van-border-color);

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 14px;
      color: var(--van-text-color-2);
      width: 80px;
      flex-shrink: 0;
    }

    .value {
      font-size: 14px;
      color: var(--van-text-color);
      text-align: right;
      flex: 1;
    }
  }
}

.application-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .van-button {
    flex: 1;
    max-width: 100px;
  }
}
</style>

<route lang="json5">
{
  name: 'Progress',
  meta: {
    title: '进度查询'
  }
}
</route>
