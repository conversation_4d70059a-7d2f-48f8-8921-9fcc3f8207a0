<!-- 我的页面 -->
<template>
  <div class="mine-page">
    <!-- 用户信息头部 -->
    <div class="user-header">
      <div class="user-info">
        <van-image
          class="avatar"
          :src="userInfo.avatar || '/images/default-avatar.png'"
          round
          width="60"
          height="60"
        />
        <div class="user-details">
          <h3 class="username">{{ userInfo.name || '**民' }}</h3>
        </div>
      </div>
      <!-- 房屋图标装饰 -->
      <div class="house-decoration">
        <div class="house-icon">
          <van-icon name="home-o" />
        </div>
        <div class="coin-icon">
          <van-icon name="gold-coin-o" />
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="个人信息"
          icon="contact"
          is-link
          @click="navigateTo('/mine/profile')"
        />
        <van-cell
          title="补贴申报记录"
          icon="orders-o"
          is-link
          @click="navigateTo('/progress')"
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const userInfo = reactive({
  name: '**民',
  avatar: '',
  isLoggedIn: true
})

// 导航方法
const navigateTo = (path) => {
  router.push(path)
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 这里应该调用获取用户信息的API
    // const response = await userApi.getUserInfo()
    // userInfo.value = response

    // Mock数据
    console.log('获取用户信息')
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})

onActivated(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.mine-page {
}

// 用户信息头部
.user-header {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f00 100%);
  padding: 40px 16px 60px 16px;
  position: relative;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  color: white;
  position: relative;
  z-index: 2;
}

.avatar {
  margin-right: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: white;
}

// 房屋装饰
.house-decoration {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.house-icon {
  font-size: 80px;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: -20px;

  .van-icon {
    font-size: 80px;
  }
}

.coin-icon {
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 24px;
  color: #ffd700;

  .van-icon {
    font-size: 24px;
  }
}

// 功能菜单
.menu-section {
  margin: -30px 16px 16px 16px;
  position: relative;
  z-index: 3;
}

// 自定义cell样式
:deep(.van-cell) {
  padding: 16px;

  .van-cell__title {
    font-size: 15px;
  }

  .van-cell__left-icon {
    margin-right: 12px;
    font-size: 18px;
    color: var(--van-text-color-2);
  }
}

:deep(.van-cell-group) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
}
</style>

<route lang="json5">
{
  name: 'Mine',
  meta: {
    title: '我的'
  }
}
</route>
