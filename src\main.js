import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from '@/router'
import pinia from '@/stores'
import Vant from 'vant';
import 'vant/lib/index.css';
import './style/theme.css';
import http from '@/utils/http'
import { setupDirectives } from '@/directives'

//请求挂载到全局
window.$http = http
const app =createApp(App)

app.use(router)
app.use(pinia)
app.use(Vant)

// 注册自定义指令
setupDirectives(app)

app.mount('#app')
