<!-- 个人信息页面 -->
<template>
  <div class="profile-page">
    <div class="profile-content">
      <van-cell-group>
        <van-cell
          title="姓名"
          :value="userInfo.name"
          readonly
        />
        <van-cell
          title="身份证号码"
          :value="userInfo.idCard"
          readonly
        />
        <van-cell
          title="手机号码"
          :value="userInfo.phone"
          readonly
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, onActivated } from 'vue'

// 响应式数据
const userInfo = reactive({
  name: '**民',
  idCard: '34567****23456784',
  phone: '187****4567'
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 这里应该调用获取用户信息的API
    // const response = await userApi.getUserInfo()
    // Object.assign(userInfo, response)
    
    // Mock数据
    console.log('获取用户信息')
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})

onActivated(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.profile-page {
}

.profile-content {
  padding: 16px;
}

// 自定义cell样式
:deep(.van-cell) {
  padding: 16px;
  
  .van-cell__title {
    font-size: 15px;
    color: var(--van-text-color);
    width: 100px;
    flex-shrink: 0;
  }
  
  .van-cell__value {
    font-size: 15px;
    color: var(--van-text-color);
    text-align: right;
  }
}

:deep(.van-cell-group) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
}
</style>

<route lang="json5">
{
  name: 'MineProfile',
  meta: {
    title: '个人信息'
  }
}
</route>
