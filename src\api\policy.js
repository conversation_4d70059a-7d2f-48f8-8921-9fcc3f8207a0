/**
 * 政策相关API接口
 */

export const policyApi = {
  /**
   * 获取政策列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.keyword] - 搜索关键词
   * @param {number} [params.categoryId] - 分类ID
   * @returns {Promise} 返回政策列表
   */
  getPolicyList: (params) => {
    return window.$http.fetch('/api/policies', params)
  },

  /**
   * 获取政策详情
   * @param {string} id - 政策ID
   * @returns {Promise} 返回政策详情
   */
  getPolicyDetail: (id) => {
    return window.$http.fetch(`/api/policies/${id}`)
  },

  /**
   * 收藏/取消收藏政策
   * @param {string} id - 政策ID
   * @param {boolean} isCollect - 是否收藏
   * @returns {Promise} 返回操作结果
   */
  toggleCollectPolicy: (id, isCollect) => {
    return window.$http.post(`/api/policies/${id}/collect`, { isCollect })
  },

  /**
   * 获取政策分类列表
   * @returns {Promise} 返回分类列表
   */
  getPolicyCategories: () => {
    return window.$http.fetch('/api/policies/categories')
  },

  /**
   * 获取相关政策推荐
   * @param {string} id - 当前政策ID
   * @returns {Promise} 返回相关政策列表
   */
  getRelatedPolicies: (id) => {
    return window.$http.fetch(`/api/policies/${id}/related`)
  }
}
