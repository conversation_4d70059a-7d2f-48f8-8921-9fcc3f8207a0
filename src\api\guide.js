/**
 * 申报指南相关API接口
 */

export const guideApi = {
  /**
   * 获取申报指南列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.keyword] - 搜索关键词
   * @param {number} [params.categoryId] - 分类ID
   * @returns {Promise} 返回指南列表
   */
  getGuideList: (params) => {
    return window.$http.fetch('/api/guides', params)
  },

  /**
   * 获取申报指南详情
   * @param {string} id - 指南ID
   * @returns {Promise} 返回指南详情
   */
  getGuideDetail: (id) => {
    return window.$http.fetch(`/api/guides/${id}`)
  },

  /**
   * 收藏/取消收藏指南
   * @param {string} id - 指南ID
   * @param {boolean} isCollect - 是否收藏
   * @returns {Promise} 返回操作结果
   */
  toggleCollectGuide: (id, isCollect) => {
    return window.$http.post(`/api/guides/${id}/collect`, { isCollect })
  },

  /**
   * 获取申报指南分类列表
   * @returns {Promise} 返回分类列表
   */
  getGuideCategories: () => {
    return window.$http.fetch('/api/guides/categories')
  },

  /**
   * 获取相关指南推荐
   * @param {string} id - 当前指南ID
   * @returns {Promise} 返回相关指南列表
   */
  getRelatedGuides: (id) => {
    return window.$http.fetch(`/api/guides/${id}/related`)
  },

  /**
   * 下载指南附件
   * @param {string} fileId - 文件ID
   * @returns {Promise} 返回文件下载链接
   */
  downloadFile: (fileId) => {
    return window.$http.fetch(`/api/guides/files/${fileId}/download`)
  }
}
